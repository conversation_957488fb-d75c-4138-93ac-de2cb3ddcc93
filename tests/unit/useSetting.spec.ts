import { describe, it, expect, vi, beforeEach, afterEach, beforeAll } from 'vitest'
import { useSetting } from '../../src/hooks/useSetting'
import { useSettingStore } from '../../src/store/useSettingStore'
import { useDashboardStore } from '../../src/store/useDashboardStore'
import { WriteData } from '../../src/const/ble.const'
import { GearDirection, LightDirection } from '../../src/const/bike.const'

// Mock Vue composition API
vi.mock('vue', () => ({
  ref: vi.fn((value) => ({ value })),
  onMounted: vi.fn((callback) => callback()),
  computed: vi.fn((getter) => ({ value: getter() }))
}))

// Mock Pinia
vi.mock('pinia', () => ({
  storeToRefs: vi.fn((store) => {
    const refs = {}
    Object.keys(store).forEach(key => {
      if (typeof store[key] !== 'function') {
        refs[key] = { value: store[key] }
      }
    })
    return refs
  })
}))

// Mock stores
const mockSettingStore = {
  dimension: 5,
  maxSpeed: 72,
  p1: 88,
  p2: 1,
  p3: 1,
  p4: 0,
  p5: 15,
  c1: 7,
  c2: 0,
  c3: 8,
  c4: 4,
  c5: 10,
  c7: 0,
  c12: 4,
  c13: 1,
  c14: 2,
  percent: 50,
  handlebarMaxSpeed: 20
}

const mockDashboardStore = {
  gearPosition: 0,
  lightStatus: 0,
  setGearPosition: vi.fn(),
  setLightStatus: vi.fn()
}

vi.mock('../../src/store/useSettingStore', () => ({
  useSettingStore: vi.fn(() => mockSettingStore)
}))

vi.mock('../../src/store/useDashboardStore', () => ({
  useDashboardStore: vi.fn(() => mockDashboardStore)
}))

// Mock runtime diagnostics
vi.mock('../../src/utils/runtimeDiagnostics', () => ({
  globalDiagnostics: {
    isMonitoring: { value: false },
    logDiagnostic: vi.fn()
  }
}))

describe('useSetting', () => {
  let setting: ReturnType<typeof useSetting>
  let consoleSpy: any

  // 创建初始WriteData的辅助函数
  const createInitialWriteData = () => [...WriteData]

  beforeAll(() => {
    console.log('开始运行 useSetting 测试套件')
  })

  beforeEach(() => {
    // 重置所有mock
    vi.clearAllMocks()
    
    // 重置store状态
    Object.assign(mockSettingStore, {
      dimension: 5,
      maxSpeed: 72,
      p1: 88,
      p2: 1,
      p3: 1,
      p4: 0,
      p5: 15,
      c1: 7,
      c2: 0,
      c3: 8,
      c4: 4,
      c5: 10,
      c7: 0,
      c12: 4,
      c13: 1,
      c14: 2,
      percent: 50,
      handlebarMaxSpeed: 20
    })

    mockDashboardStore.gearPosition = 0
    mockDashboardStore.lightStatus = 0

    // 创建setting实例
    setting = useSetting()
    
    // Mock console方法
    consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
    vi.spyOn(console, 'warn').mockImplementation(() => {})
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('初始化', () => {
    it('应该正确初始化writeData', () => {
      expect(setting.writeData.value).toBeDefined()
      expect(setting.writeData.value.length).toBe(18)
      
      // 验证初始数据与WriteData常量一致
      const initialData = createInitialWriteData()
      expect(setting.writeData.value).toEqual(initialData)
    })

    it('应该返回正确的方法', () => {
      expect(setting.changeGearPosition).toBeDefined()
      expect(setting.changeLightStatus).toBeDefined()
      expect(setting.setMaxSpeed).toBeDefined()
      expect(setting.updateSetting).toBeDefined()
      expect(typeof setting.changeGearPosition).toBe('function')
      expect(typeof setting.changeLightStatus).toBe('function')
      expect(typeof setting.setMaxSpeed).toBe('function')
      expect(typeof setting.updateSetting).toBe('function')
    })
  })

  describe('Int2Bytes 工具函数', () => {
    it('应该正确转换数字到字节', () => {
      // 通过调用内部方法来测试Int2Bytes
      // 由于Int2Bytes是内部函数，我们通过setP5来间接测试
      mockSettingStore.p5 = 15
      setting = useSetting()
      
      // 验证P5设置后的结果
      expect(setting.writeData.value[0]).toBe(15)
    })

    it('应该正确处理字符串输入', () => {
      mockSettingStore.p5 = '25' as any
      setting = useSetting()
      
      expect(setting.writeData.value[0]).toBe(25)
    })

    it('应该正确处理边界值', () => {
      mockSettingStore.p5 = 255
      setting = useSetting()
      
      expect(setting.writeData.value[0]).toBe(255)
    })

    it('应该正确处理超出范围的值', () => {
      mockSettingStore.p5 = 300
      setting = useSetting()
      
      // 300 & 0xFF = 44
      expect(setting.writeData.value[0]).toBe(44)
    })

    it('应该正确处理无效值', () => {
      mockSettingStore.p5 = NaN
      setting = useSetting()
      
      expect(setting.writeData.value[0]).toBe(0)
    })
  })

  describe('getDimension 方法', () => {
    it('应该正确处理小于10的轮径值', () => {
      mockSettingStore.dimension = 5
      setting = useSetting()
      
      // 通过setMaxSpeed间接测试getDimension
      setting.setMaxSpeed()
      
      // 验证轮径扩展位为0（小于10不需要扩展）
      const byte4 = setting.writeData.value[4]
      const expand = byte4 & 0x80
      expect(expand).toBe(0)
    })

    it('应该正确处理大于等于10的轮径值', () => {
      mockSettingStore.dimension = 15
      setting = useSetting()
      
      setting.setMaxSpeed()
      
      // 验证轮径扩展位为0x80（大于等于10需要扩展）
      const byte4 = setting.writeData.value[4]
      const expand = byte4 & 0x80
      expect(expand).toBe(0x80)
    })
  })

  describe('setMaxSpeed 方法', () => {
    it('应该正确设置小于42的最大速度', () => {
      mockSettingStore.maxSpeed = 30
      mockSettingStore.dimension = 5
      mockSettingStore.p2 = 1
      mockSettingStore.p3 = 1
      mockSettingStore.p4 = 0
      setting = useSetting()

      setting.setMaxSpeed()

      // 验证速度设置
      const speed = 30 - 10 // 20
      expect(speed).toBeLessThan(32)

      // 验证字节2和字节4的设置
      expect(setting.writeData.value[2]).toBeDefined()
      expect(setting.writeData.value[4]).toBeDefined()
    })

    it('应该正确设置32-63范围的最大速度', () => {
      mockSettingStore.maxSpeed = 50 // speed = 40
      mockSettingStore.dimension = 5
      mockSettingStore.p2 = 1
      mockSettingStore.p3 = 1
      mockSettingStore.p4 = 0
      setting = useSetting()

      setting.setMaxSpeed()

      const speed = 50 - 10 // 40
      expect(speed).toBeGreaterThanOrEqual(32)
      expect(speed).toBeLessThan(64)

      // 验证限速位设置为0x20
      const byte4 = setting.writeData.value[4]
      const limitSpeed = byte4 & 0x20
      expect(limitSpeed).toBe(0x20)
    })

    it('应该正确处理速度为42的边界情况', () => {
      mockSettingStore.maxSpeed = 42 // speed = 32
      mockSettingStore.dimension = 5
      setting = useSetting()

      setting.setMaxSpeed()

      // 验证secondData为0x5（speedDiff为0的特殊情况）
      expect(setting.writeData.value[2]).toBe(0x5)
    })
  })

  describe('参数设置方法', () => {
    it('setP1 应该正确设置字节3', () => {
      mockSettingStore.p1 = 100
      setting = useSetting()

      expect(setting.writeData.value[3]).toBe(100)
    })

    it('setP5 应该正确设置字节0', () => {
      mockSettingStore.p5 = 20
      setting = useSetting()

      expect(setting.writeData.value[0]).toBe(20)
    })

    it('setC1C2 应该正确设置字节6', () => {
      mockSettingStore.c1 = 7
      mockSettingStore.c2 = 3
      setting = useSetting()

      // c1左移3位 + c2 = (7 << 3) + 3 = 56 + 3 = 59
      expect(setting.writeData.value[6]).toBe(59)
    })

    it('setC3 应该正确设置档位', () => {
      mockSettingStore.c3 = 3
      setting = useSetting()

      expect(mockDashboardStore.setGearPosition).toHaveBeenCalledWith(3)
    })

    it('setC3 应该跳过档位8', () => {
      mockSettingStore.c3 = 8
      setting = useSetting()

      // 档位8应该被跳过，不调用setGearPosition
      expect(mockDashboardStore.setGearPosition).not.toHaveBeenCalled()
    })

    it('setC5C14 应该正确设置字节7', () => {
      mockSettingStore.c5 = 10
      mockSettingStore.c14 = 2
      setting = useSetting()

      // 128 + (c14 << 5) + c5 = 128 + (2 << 5) + 10 = 128 + 64 + 10 = 202
      expect(setting.writeData.value[7]).toBe(202)
    })

    it('setC4C7C12 应该正确设置字节8', () => {
      mockSettingStore.c4 = 4
      mockSettingStore.c7 = 1
      mockSettingStore.c12 = 5
      setting = useSetting()

      // (c4 << 5) + (c7 << 3) + c12 = (4 << 5) + (1 << 3) + 5 = 128 + 8 + 5 = 141
      expect(setting.writeData.value[8]).toBe(141)
    })

    it('setC13 应该正确设置字节10', () => {
      mockSettingStore.c13 = 2
      setting = useSetting()

      // (c13 << 2) + 1 = (2 << 2) + 1 = 8 + 1 = 9
      expect(setting.writeData.value[10]).toBe(9)
    })

    it('setPercent 应该正确设置字节11', () => {
      mockSettingStore.percent = 75
      setting = useSetting()

      expect(setting.writeData.value[11]).toBe(75)
    })

    it('setHandlebar 应该正确设置字节9', () => {
      mockSettingStore.handlebarMaxSpeed = 25
      setting = useSetting()

      expect(setting.writeData.value[9]).toBe(25)
    })
  })

  describe('档位和灯光控制', () => {
    it('changeGearPosition 应该正确设置档位', () => {
      setting.changeGearPosition(GearDirection.positionThree)

      expect(mockDashboardStore.setGearPosition).toHaveBeenCalledWith(GearDirection.positionThree)
    })

    it('changeLightStatus 应该正确切换灯光状态', () => {
      // 测试开灯
      setting.changeLightStatus(LightDirection.on)
      expect(mockDashboardStore.setLightStatus).toHaveBeenCalledWith(LightDirection.on)

      // 测试关灯
      setting.changeLightStatus(LightDirection.off)
      expect(mockDashboardStore.setLightStatus).toHaveBeenCalledWith(LightDirection.off)
    })
  })

  describe('校验和计算', () => {
    it('应该正确计算字节16的校验值', () => {
      // 设置一些测试数据
      mockSettingStore.p1 = 100
      mockSettingStore.p5 = 20
      setting = useSetting()

      // 手动计算期望的校验值
      const expectedChecksum =
        setting.writeData.value[1] ^
        setting.writeData.value[2] ^
        setting.writeData.value[3] ^
        setting.writeData.value[4] ^
        setting.writeData.value[6] ^
        setting.writeData.value[7] ^
        setting.writeData.value[8] ^
        setting.writeData.value[9] ^
        setting.writeData.value[10] ^
        setting.writeData.value[11] ^
        setting.writeData.value[12] ^
        setting.writeData.value[13] ^
        setting.writeData.value[14] ^
        setting.writeData.value[15]

      expect(setting.writeData.value[16]).toBe(expectedChecksum)
    })

    it('updateSetting 后应该有正确的校验值', () => {
      // 修改一些设置
      mockSettingStore.maxSpeed = 60
      mockSettingStore.p1 = 90
      mockSettingStore.c1 = 5
      setting = useSetting()

      setting.updateSetting()

      // 验证校验值是否正确计算
      const calculatedChecksum =
        setting.writeData.value[1] ^
        setting.writeData.value[2] ^
        setting.writeData.value[3] ^
        setting.writeData.value[4] ^
        setting.writeData.value[6] ^
        setting.writeData.value[7] ^
        setting.writeData.value[8] ^
        setting.writeData.value[9] ^
        setting.writeData.value[10] ^
        setting.writeData.value[11] ^
        setting.writeData.value[12] ^
        setting.writeData.value[13] ^
        setting.writeData.value[14] ^
        setting.writeData.value[15]

      expect(setting.writeData.value[16]).toBe(calculatedChecksum)
    })
  })

  describe('updateSetting 方法', () => {
    it('应该按正确顺序更新所有设置', () => {
      // 设置测试数据
      mockSettingStore.maxSpeed = 50
      mockSettingStore.p1 = 95
      mockSettingStore.p5 = 18
      mockSettingStore.c1 = 6
      mockSettingStore.c2 = 2
      mockSettingStore.c3 = 3
      mockSettingStore.c5 = 12
      mockSettingStore.c14 = 3
      mockSettingStore.percent = 60
      mockSettingStore.handlebarMaxSpeed = 22

      setting = useSetting()

      // 验证各个字节都被正确设置
      expect(setting.writeData.value[0]).toBe(18) // P5
      expect(setting.writeData.value[3]).toBe(95) // P1
      expect(setting.writeData.value[6]).toBe((6 << 3) + 2) // C1C2
      expect(setting.writeData.value[7]).toBe(128 + (3 << 5) + 12) // C5C14
      expect(setting.writeData.value[9]).toBe(22) // handlebarMaxSpeed
      expect(setting.writeData.value[11]).toBe(60) // percent
    })

    it('应该保留导航数据不被覆盖', () => {
      // 设置一些导航数据
      setting.writeData.value[12] = 0x81 // 带镜像位的导航数据
      setting.writeData.value[13] = 100
      setting.writeData.value[14] = 50
      setting.writeData.value[15] = 200

      const originalNavigationData = {
        byte12: setting.writeData.value[12],
        byte13: setting.writeData.value[13],
        byte14: setting.writeData.value[14],
        byte15: setting.writeData.value[15]
      }

      setting.updateSetting()

      // 验证导航数据被保留
      expect(setting.writeData.value[12]).toBe(originalNavigationData.byte12)
      expect(setting.writeData.value[13]).toBe(originalNavigationData.byte13)
      expect(setting.writeData.value[14]).toBe(originalNavigationData.byte14)
      expect(setting.writeData.value[15]).toBe(originalNavigationData.byte15)
    })

    it('应该调用回调函数', () => {
      const mockCallback = vi.fn()
      setting = useSetting(mockCallback)

      setting.updateSetting()

      expect(mockCallback).toHaveBeenCalled()
    })

    it('应该处理回调函数异常', () => {
      const mockCallback = vi.fn(() => {
        throw new Error('回调函数错误')
      })

      const warnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      setting = useSetting(mockCallback)
      setting.updateSetting()

      expect(warnSpy).toHaveBeenCalledWith('数据更新回调执行失败:', expect.any(Error))
    })
  })

  describe('边界值测试', () => {
    it('应该正确处理最小值', () => {
      mockSettingStore.p1 = 0
      mockSettingStore.p5 = 0
      mockSettingStore.c1 = 0
      mockSettingStore.c2 = 0
      mockSettingStore.percent = 0
      mockSettingStore.handlebarMaxSpeed = 0

      setting = useSetting()

      expect(setting.writeData.value[0]).toBe(0) // P5
      expect(setting.writeData.value[3]).toBe(0) // P1
      expect(setting.writeData.value[6]).toBe(0) // C1C2
      expect(setting.writeData.value[9]).toBe(0) // handlebarMaxSpeed
      expect(setting.writeData.value[11]).toBe(0) // percent
    })

    it('应该正确处理最大值', () => {
      mockSettingStore.p1 = 255
      mockSettingStore.p5 = 255
      mockSettingStore.c1 = 7 // 最大3位值
      mockSettingStore.c2 = 7 // 最大3位值
      mockSettingStore.percent = 255
      mockSettingStore.handlebarMaxSpeed = 255

      setting = useSetting()

      expect(setting.writeData.value[0]).toBe(255) // P5
      expect(setting.writeData.value[3]).toBe(255) // P1
      expect(setting.writeData.value[6]).toBe((7 << 3) + 7) // C1C2
      expect(setting.writeData.value[9]).toBe(255) // handlebarMaxSpeed
      expect(setting.writeData.value[11]).toBe(255) // percent
    })

    it('应该正确处理负数', () => {
      mockSettingStore.p1 = -10
      mockSettingStore.p5 = -5

      setting = useSetting()

      // 负数应该被转换为正确的字节值
      expect(setting.writeData.value[0]).toBe((-5) & 0xFF) // P5
      expect(setting.writeData.value[3]).toBe((-10) & 0xFF) // P1
    })
  })

  describe('数据完整性验证', () => {
    it('writeData 应该始终有18个字节', () => {
      setting = useSetting()

      expect(setting.writeData.value.length).toBe(18)

      // 更新设置后长度应该保持不变
      setting.updateSetting()
      expect(setting.writeData.value.length).toBe(18)
    })

    it('所有字节值应该在0-255范围内', () => {
      // 设置一些极端值
      mockSettingStore.maxSpeed = 100
      mockSettingStore.p1 = 300 // 超出范围
      mockSettingStore.p5 = -50 // 负数

      setting = useSetting()

      // 验证所有字节都在有效范围内
      for (let i = 0; i < setting.writeData.value.length; i++) {
        const value = setting.writeData.value[i]
        expect(value).toBeGreaterThanOrEqual(0)
        expect(value).toBeLessThanOrEqual(255)
        expect(Number.isInteger(value)).toBe(true)
      }
    })

    it('字节17应该始终是0x0e', () => {
      setting = useSetting()

      expect(setting.writeData.value[17]).toBe(0x0e)

      // 更新设置后应该保持不变
      setting.updateSetting()
      expect(setting.writeData.value[17]).toBe(0x0e)
    })
  })

  describe('错误处理', () => {
    it('应该处理undefined值', () => {
      mockSettingStore.p1 = undefined as any
      mockSettingStore.p5 = undefined as any

      setting = useSetting()

      // undefined应该被转换为0
      expect(setting.writeData.value[0]).toBe(0) // P5
      expect(setting.writeData.value[3]).toBe(0) // P1
    })

    it('应该处理null值', () => {
      mockSettingStore.p1 = null as any
      mockSettingStore.p5 = null as any

      setting = useSetting()

      // null应该被转换为0
      expect(setting.writeData.value[0]).toBe(0) // P5
      expect(setting.writeData.value[3]).toBe(0) // P1
    })

    it('应该处理非数字字符串', () => {
      mockSettingStore.p1 = 'abc' as any
      mockSettingStore.p5 = 'xyz' as any

      setting = useSetting()

      // 非数字字符串应该被转换为0
      expect(setting.writeData.value[0]).toBe(0) // P5
      expect(setting.writeData.value[3]).toBe(0) // P1
    })
  })

  describe('HexString2Bytes 工具函数', () => {
    // 由于HexString2Bytes是内部函数，我们需要通过其他方式测试
    // 这里我们创建一个测试用的setting实例来访问内部方法
    let testSetting: any

    beforeEach(() => {
      testSetting = useSetting()
      // 通过反射获取内部方法（仅用于测试）
      const settingInstance = testSetting as any
      // 我们无法直接访问内部函数，但可以测试其行为影响
    })

    it('应该能处理有效的十六进制字符串', () => {
      // 由于无法直接测试HexString2Bytes，我们测试其预期行为
      // 这个测试主要验证相关功能的存在性
      expect(testSetting.writeData.value).toBeDefined()
    })
  })

  describe('updateFirstIndexOfData 方法', () => {
    it('应该正确更新字节1（档位+灯光）', () => {
      mockDashboardStore.gearPosition = 2
      mockDashboardStore.lightStatus = false
      setting = useSetting()

      // 手动调用changeGearPosition来触发updateFirstIndexOfData
      setting.changeGearPosition(2)

      // 验证字节1 = GearDirection.positionTwo + LightDirection.off
      expect(setting.writeData.value[1]).toBe(GearDirection.positionTwo + LightDirection.off)
    })

    it('应该正确处理档位+灯光组合', () => {
      mockDashboardStore.gearPosition = 3
      mockDashboardStore.lightStatus = true
      setting = useSetting()

      setting.changeGearPosition(3)
      setting.changeLightStatus(true)

      // 验证字节1 = GearDirection.positionThree + LightDirection.on
      expect(setting.writeData.value[1]).toBe(GearDirection.positionThree + LightDirection.on)
    })

    it('应该正确处理所有档位', () => {
      const testCases = [
        { gear: 0, expected: GearDirection.positionZero },
        { gear: 1, expected: GearDirection.positionOne },
        { gear: 2, expected: GearDirection.positionTwo },
        { gear: 3, expected: GearDirection.positionThree },
        { gear: 4, expected: GearDirection.positionFour },
        { gear: 5, expected: GearDirection.positionFive },
        { gear: 99, expected: GearDirection.positionFive } // 默认情况
      ]

      testCases.forEach(({ gear, expected }) => {
        mockDashboardStore.gearPosition = gear
        mockDashboardStore.lightStatus = false
        setting = useSetting()

        setting.changeGearPosition(gear)

        expect(setting.writeData.value[1]).toBe(expected + LightDirection.off)
      })
    })
  })

  describe('updateFiveIndexOfData 方法', () => {
    it('应该正确计算字节5的校验值', () => {
      setting = useSetting()

      // 手动计算期望的字节5值
      const expectedByte5 =
        setting.writeData.value[1] ^
        setting.writeData.value[2] ^
        setting.writeData.value[3] ^
        setting.writeData.value[4] ^
        setting.writeData.value[6] ^
        setting.writeData.value[7] ^
        setting.writeData.value[8] ^
        setting.writeData.value[9] ^
        setting.writeData.value[10] ^
        setting.writeData.value[11]

      expect(setting.writeData.value[5]).toBe(expectedByte5)
    })

    it('应该在每次参数更新后重新计算字节5', () => {
      setting = useSetting()
      const originalByte5 = setting.writeData.value[5]

      // 修改一个参数
      mockSettingStore.p1 = 100
      setting = useSetting()

      // 字节5应该发生变化
      expect(setting.writeData.value[5]).not.toBe(originalByte5)
    })

    it('应该调用回调函数', () => {
      const mockCallback = vi.fn()
      setting = useSetting(mockCallback)

      // 修改参数触发updateFiveIndexOfData
      mockSettingStore.p1 = 95
      setting = useSetting(mockCallback)

      // 由于onMounted会调用updateSetting，而updateSetting会多次调用updateFiveIndexOfData
      expect(mockCallback).toHaveBeenCalled()
    })
  })

  describe('validateWriteData 方法', () => {
    it('应该修复无效的字节值', () => {
      setting = useSetting()

      // 保存原始的P5值，因为updateSetting会重新设置字节0
      const originalP5 = mockSettingStore.p5

      // 手动设置一些无效值来测试validateWriteData
      setting.writeData.value[12] = NaN // 使用导航数据区域进行测试
      setting.writeData.value[13] = -10
      setting.writeData.value[14] = 300
      setting.writeData.value[15] = null as any

      // 调用updateSetting会触发validateWriteData
      setting.updateSetting()

      // 验证无效值被修复（注意updateSetting会重新计算这些值）
      expect(setting.writeData.value[12]).toBeGreaterThanOrEqual(0) // NaN -> 0
      expect(setting.writeData.value[13]).toBeGreaterThanOrEqual(0) // -10 -> 正数
      expect(setting.writeData.value[14]).toBeLessThanOrEqual(255) // 300 -> <=255
      expect(setting.writeData.value[15]).toBeGreaterThanOrEqual(0) // null -> 0
    })

    it('应该保持有效值不变', () => {
      setting = useSetting()

      // 使用导航数据区域进行测试，避免被设置更新覆盖
      const validValues = [0, 1, 128, 255]
      const testIndices = [12, 13, 14, 15] // 使用导航数据区域

      validValues.forEach((value, index) => {
        setting.writeData.value[testIndices[index]] = value
      })

      // 保存测试值
      const savedValues = testIndices.map(i => setting.writeData.value[i])

      setting.updateSetting()

      // 验证这些位置的值在合理范围内（可能被updateSetting重新计算）
      testIndices.forEach((testIndex) => {
        const value = setting.writeData.value[testIndex]
        expect(value).toBeGreaterThanOrEqual(0)
        expect(value).toBeLessThanOrEqual(255)
        expect(Number.isInteger(value)).toBe(true)
      })
    })
  })

  describe('速度范围边界测试', () => {
    it('应该正确处理速度>=64的情况', () => {
      mockSettingStore.maxSpeed = 80 // speed = 70, >= 64
      mockSettingStore.dimension = 5
      setting = useSetting()

      setting.setMaxSpeed()

      // 速度>=64时，应该不设置任何特殊处理（根据代码逻辑）
      // 只验证不会抛出错误
      expect(setting.writeData.value).toBeDefined()
    })

    it('应该正确处理速度为负数的情况', () => {
      mockSettingStore.maxSpeed = 5 // speed = -5
      mockSettingStore.dimension = 5
      setting = useSetting()

      setting.setMaxSpeed()

      // 负速度应该被正确处理
      expect(setting.writeData.value).toBeDefined()
    })
  })

  describe('集成测试', () => {
    it('应该正确处理完整的设置更新流程', () => {
      // 设置完整的测试数据
      const testSettings = {
        maxSpeed: 65,
        dimension: 12,
        p1: 85,
        p2: 2,
        p3: 1,
        p4: 1,
        p5: 16,
        c1: 6,
        c2: 1,
        c3: 2,
        c4: 3,
        c5: 8,
        c7: 1,
        c12: 3,
        c13: 2,
        c14: 1,
        percent: 45,
        handlebarMaxSpeed: 18
      }

      Object.assign(mockSettingStore, testSettings)
      setting = useSetting()

      // 验证所有设置都被正确应用
      expect(setting.writeData.value[0]).toBe(16) // P5
      expect(setting.writeData.value[3]).toBe(85) // P1
      expect(setting.writeData.value[6]).toBe((6 << 3) + 1) // C1C2
      expect(setting.writeData.value[7]).toBe(128 + (1 << 5) + 8) // C5C14
      expect(setting.writeData.value[8]).toBe((3 << 5) + (1 << 3) + 3) // C4C7C12
      expect(setting.writeData.value[9]).toBe(18) // handlebarMaxSpeed
      expect(setting.writeData.value[10]).toBe((2 << 2) + 1) // C13
      expect(setting.writeData.value[11]).toBe(45) // percent

      // 验证校验和正确
      const expectedChecksum =
        setting.writeData.value[1] ^
        setting.writeData.value[2] ^
        setting.writeData.value[3] ^
        setting.writeData.value[4] ^
        setting.writeData.value[6] ^
        setting.writeData.value[7] ^
        setting.writeData.value[8] ^
        setting.writeData.value[9] ^
        setting.writeData.value[10] ^
        setting.writeData.value[11] ^
        setting.writeData.value[12] ^
        setting.writeData.value[13] ^
        setting.writeData.value[14] ^
        setting.writeData.value[15]

      expect(setting.writeData.value[16]).toBe(expectedChecksum)
    })

    it('应该正确处理诊断日志记录', () => {
      // 由于诊断日志的mock已经在beforeEach中设置，我们只需要验证功能正常
      // 这个测试主要验证诊断相关代码不会抛出错误
      setting = useSetting()

      // 验证设置更新过程不会因为诊断日志而失败
      expect(() => {
        setting.updateSetting()
      }).not.toThrow()

      // 验证writeData仍然有效
      expect(setting.writeData.value).toBeDefined()
      expect(setting.writeData.value.length).toBe(18)
    })
  })

  describe('SettingPage.vue 实际使用场景测试', () => {
    it('应该正确处理 SettingPage 中的 updateSetting 调用', () => {
      // 模拟 SettingPage 中的使用方式
      const { updateSetting, writeData } = useSetting()

      // 验证返回的方法和数据
      expect(updateSetting).toBeDefined()
      expect(typeof updateSetting).toBe('function')
      expect(writeData.value).toBeDefined()
      expect(writeData.value.length).toBe(18)

      // 调用 updateSetting（模拟 SettingPage 中的调用）
      expect(() => {
        updateSetting()
      }).not.toThrow()
    })

    it('应该正确处理导航数据保留场景', () => {
      // 模拟 SettingPage 中保留导航数据的场景
      setting = useSetting()

      // 设置一些导航数据（模拟从蓝牙服务获取的数据）
      const mockNavigationData = [0x81, 100, 50, 200] // 带镜像位的导航数据
      setting.writeData.value[12] = mockNavigationData[0]
      setting.writeData.value[13] = mockNavigationData[1]
      setting.writeData.value[14] = mockNavigationData[2]
      setting.writeData.value[15] = mockNavigationData[3]

      // 调用 updateSetting（模拟 SettingPage 中的调用）
      setting.updateSetting()

      // 验证导航数据被保留
      expect(setting.writeData.value[12]).toBe(mockNavigationData[0])
      expect(setting.writeData.value[13]).toBe(mockNavigationData[1])
      expect(setting.writeData.value[14]).toBe(mockNavigationData[2])
      expect(setting.writeData.value[15]).toBe(mockNavigationData[3])
    })

    it('应该正确处理镜像位保留', () => {
      // 模拟 SettingPage 中镜像位处理的场景
      setting = useSetting()

      // 设置镜像位（字节12的第7位）
      setting.writeData.value[12] = 0x80 | 0x01 // 镜像位开启 + 方向位

      setting.updateSetting()

      // 验证镜像位被保留
      expect(setting.writeData.value[12] & 0x80).toBe(0x80)
    })

    it('应该正确处理 Tab1Page 中的档位变化', () => {
      // 模拟 Tab1Page 中档位变化的场景
      const { changeGearPosition } = useSetting()

      // 测试档位增加
      changeGearPosition(3)
      expect(mockDashboardStore.setGearPosition).toHaveBeenCalledWith(3)

      // 测试档位减少
      changeGearPosition(1)
      expect(mockDashboardStore.setGearPosition).toHaveBeenCalledWith(1)
    })

    it('应该正确处理设置参数的实时更新', () => {
      // 模拟用户在 SettingPage 中修改参数的场景
      const testScenarios = [
        { param: 'maxSpeed', value: 60, expectedByte: 2 },
        { param: 'p1', value: 90, expectedByte: 3 },
        { param: 'p5', value: 18, expectedByte: 0 },
        { param: 'handlebarMaxSpeed', value: 25, expectedByte: 9 },
        { param: 'percent', value: 75, expectedByte: 11 }
      ]

      testScenarios.forEach(({ param, value, expectedByte }) => {
        // 重置mock store
        Object.assign(mockSettingStore, {
          dimension: 5,
          maxSpeed: 72,
          p1: 88,
          p2: 1,
          p3: 1,
          p4: 0,
          p5: 15,
          c1: 7,
          c2: 0,
          c3: 8,
          c4: 4,
          c5: 10,
          c7: 0,
          c12: 4,
          c13: 1,
          c14: 2,
          percent: 50,
          handlebarMaxSpeed: 20
        })

        // 修改特定参数
        mockSettingStore[param] = value

        // 创建新的setting实例
        const testSetting = useSetting()

        // 验证对应字节被正确设置
        if (param === 'maxSpeed') {
          // maxSpeed 影响多个字节，主要验证不抛出错误
          expect(testSetting.writeData.value).toBeDefined()
        } else {
          expect(testSetting.writeData.value[expectedByte]).toBe(value)
        }
      })
    })

    it('应该正确处理复杂参数的位运算', () => {
      // 测试 SettingPage 中复杂参数的组合设置
      const complexParams = {
        c1: 5,
        c2: 3,
        c5: 12,
        c14: 1,
        c4: 2,
        c7: 1,
        c12: 6,
        c13: 3
      }

      Object.assign(mockSettingStore, complexParams)
      setting = useSetting()

      // 验证复杂位运算结果
      expect(setting.writeData.value[6]).toBe((5 << 3) + 3) // C1C2
      expect(setting.writeData.value[7]).toBe(128 + (1 << 5) + 12) // C5C14
      expect(setting.writeData.value[8]).toBe((2 << 5) + (1 << 3) + 6) // C4C7C12
      expect(setting.writeData.value[10]).toBe((3 << 2) + 1) // C13
    })

    it('应该正确处理 restore 操作后的数据重置', () => {
      // 模拟 SettingPage 中 restore 按钮的场景
      // 先设置一些非默认值
      Object.assign(mockSettingStore, {
        maxSpeed: 50,
        p1: 100,
        p5: 25,
        percent: 80
      })

      setting = useSetting()
      const modifiedData = [...setting.writeData.value]

      // 重置为默认值（模拟 store.$reset()）
      Object.assign(mockSettingStore, {
        maxSpeed: 72,
        p1: 88,
        p5: 15,
        percent: 50
      })

      // 创建新的setting实例（模拟重置后的状态）
      const resetSetting = useSetting()

      // 验证数据确实发生了变化
      expect(resetSetting.writeData.value).not.toEqual(modifiedData)
      expect(resetSetting.writeData.value[0]).toBe(15) // P5 重置为默认值
      expect(resetSetting.writeData.value[3]).toBe(88) // P1 重置为默认值
      expect(resetSetting.writeData.value[11]).toBe(50) // percent 重置为默认值
    })

    it('应该正确处理异步保存操作', async () => {
      // 模拟 SettingPage 中 saveSettings 的异步场景
      const mockCallback = vi.fn()
      setting = useSetting(mockCallback)

      // 模拟异步保存过程
      await new Promise(resolve => {
        setting.updateSetting()
        setTimeout(resolve, 10) // 模拟异步延迟
      })

      // 验证回调被调用
      expect(mockCallback).toHaveBeenCalled()

      // 验证数据完整性
      expect(setting.writeData.value.length).toBe(18)
      expect(setting.writeData.value[17]).toBe(0x0e) // 截止位
    })
  })
})
