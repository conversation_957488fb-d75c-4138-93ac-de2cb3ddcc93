import legacy from '@vitejs/plugin-legacy'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import { defineConfig } from 'vite'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    // 只在生产环境使用legacy插件
    ...(process.env.NODE_ENV === 'production' ? [legacy({
      targets: ['defaults', 'not IE 11']
    })] : [])
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    // 启用代码分割
    rollupOptions: {
      output: {
        manualChunks: {
          // 将Vue相关库分离到单独的chunk
          vue: ['vue', 'vue-router', 'pinia'],
          // 将Ionic相关库分离
          ionic: ['@ionic/vue', '@ionic/core', '@ionic/vue-router'],
          // 将Mapbox相关库分离（这是最大的依赖）
          mapbox: ['mapbox-gl', '@mapbox/search-js-web'],
          // 将Capacitor相关库分离
          capacitor: ['@capacitor/core', '@capacitor/app', '@capacitor/haptics', '@capacitor/keyboard', '@capacitor/status-bar'],
          // 将蓝牙相关库分离
          bluetooth: ['@capacitor-community/bluetooth-le'],
          // 将工具库分离
          utils: ['@vueuse/core', '@vueuse/components'],
          // 将图表库分离
          charts: ['@antv/g2']
        }
      }
    },
    // 增加chunk大小警告限制
    chunkSizeWarningLimit: 1000,
    // 启用压缩
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // 生产环境移除console
        drop_debugger: true
      }
    },
    // 启用源码映射（开发时有用）
    sourcemap: process.env.NODE_ENV === 'development'
  },
  // 优化依赖预构建
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'pinia',
      '@ionic/vue',
      '@ionic/core',
      '@vueuse/core'
    ],
    exclude: [
      // 排除大型依赖的预构建，让它们按需加载
      'mapbox-gl',
      '@mapbox/search-js-web'
    ]
  },
  // 开发服务器优化
  server: {
    fs: {
      // 允许访问工作区根目录之外的文件
      allow: ['..']
    }
  }
})
